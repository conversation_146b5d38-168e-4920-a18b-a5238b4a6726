FROM node:20-alpine AS build
ENV NODE_ENV=production
WORKDIR /usr/src/app

# Copy root package.json and lockfile
COPY package.json ./
COPY package-lock.json ./

# Copy the docs package.json
COPY apps/backend/package.json ./apps/backend/package.json
COPY ./turbo.json ./
COPY ./packages ./packages
RUN npm install

# Build
COPY ./apps/backend ./apps/backend
RUN npm run build-backend

EXPOSE 3000
CMD ["node", "/usr/src/app/apps/backend/dist/main.js"]