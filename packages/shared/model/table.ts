/**
 * A table is an object that you can order products to.
 * It can also be a place like a bar or single person.
 * A table might also be created on the fly if it's not
 * a stationary thing or the waiter hands a ticket.
 */
export type Table = {
  /**
   * The table id is only for internal usage and should not be user facing.
   */
  id: string;
  /**
   * Table name can either be an actual name or a table number
   */
  name: string;
};
