import { Order } from "./order";
import { Product, ProductCategory } from "./product";
import { Table } from "./table";
import { User } from "./user";

/**
 * THIS IS A DEMO SETUP
 */

const user: User = {
  id: 123123,
};

const tables: Table[] = [
  {
    id: "table1",
    name: "Tisch 1",
  },
  {
    id: "table2",
    name: "Tisch 2",
  },
];

const drinksCategory: ProductCategory = {
  id: "drinks",
  name: "<PERSON>r<PERSON><PERSON><PERSON>",
};

const bier: Product = {
  id: "bier",
  name: "Bier",
  priceCents: 500,
  categoryId: drinksCategory.id,
};

const order: Order = {
  id: crypto.randomUUID(),
  userId: user.id,
  tableId: tables[1].id,
  content: [
    {
      productId: bier.id,
      items: [
        {
          productId: bier.id,
          payed: false,
          note: "Bitte ohne Schaum",
        },
      ],
    },
  ],
};
