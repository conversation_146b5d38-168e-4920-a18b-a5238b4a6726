import type { Product } from "./product";
import type { Table } from "./table";
import type { User } from "./user";

export type OrderItem = {
  /**
   * The productId maps to the ordered product
   */
  productId: Product["id"];
  /**
   * The payed flag marks if this OrderItems was already payed.
   */
  payed: boolean;
  /**
   * A not can be a modification to an ordered product.
   */
  note?: string;
};

/**
 * An order is a group of projects that was ordered by a user for a table
 */
export type Order = {
  /**
   * The order id is only for internal usage and should not be user facing.
   */
  id: string;
  /**
   * The userId maps to the user who placed the order (a cashier or bartender)
   */
  userId: User["id"];
  /**
   * The tableId maps the othe table that the order was placed to
   */
  tableId: Table["id"];
  /**
   * The order content contains all products that were ordered grouped by productId
   */
  content: {
    productId: Product["id"];
    /**
     * The items contain an array of OrderItems. They basically just resemble a single product.
     * So the quantity or an ordered product can be known by the length of the array.
     * The OrderItem can also contain a note or other modifiers for a product of an order.
     */
    items: OrderItem[];
  }[];
  /**
   * An ISO timestamp of the creation of the order
   */
  createdAt: string;
};
