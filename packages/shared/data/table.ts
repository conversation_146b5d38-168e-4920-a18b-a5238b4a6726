import * as Y from "yjs";
import { Table } from "../model";

const TABLES_NAMESPACE = "tables";

export function Tables(doc: Y.Doc) {
  const tablesMap = doc.getMap<Table>(TABLES_NAMESPACE);

  function getAll() {
    return tablesMap.values();
  }
  function getOne(tableId: Table["id"]) {
    return tablesMap.get(tableId);
  }
  function setOne(tableId: Table["id"], table: Table) {
    return tablesMap.set(tableId, { ...table, id: tableId });
  }
  function createOne(newTable: Omit<Table, "id">) {
    const id = crypto.randomUUID();
    const table: Table = {
      ...newTable,
      id,
    };
    return setOne(id, table);
  }
  function deleteOne(tableId: Table["id"]) {
    return tablesMap.delete(tableId);
  }

  function observe(cb: () => void) {
    tablesMap.observeDeep(cb);
  }

  return {
    getAll,
    getOne,
    setOne,
    createOne,
    deleteOne,
    observe,
  };
}
