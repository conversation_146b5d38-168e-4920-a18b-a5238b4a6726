import * as Y from "yjs";
import type { ProductCategory } from "../model";

const PRODUCT_CATEGORY_NAMESPACE = "product_category";

export function ProductCategories(doc: Y.Doc) {
  const productCategoriesMap = doc.getMap<ProductCategory>(
    PRODUCT_CATEGORY_NAMESPACE,
  );

  function getAll() {
    return productCategoriesMap.values();
  }
  function getOne(id: ProductCategory["id"]) {
    return productCategoriesMap.get(id);
  }
  function setOne(id: ProductCategory["id"], table: ProductCategory) {
    return productCategoriesMap.set(id, { ...table, id: id });
  }
  function createOne(newItem: Omit<ProductCategory, "id">) {
    const id = crypto.randomUUID();
    const table: ProductCategory = {
      ...newItem,
      id,
    };
    return setOne(id, table);
  }
  function deleteOne(id: ProductCategory["id"]) {
    return productCategoriesMap.delete(id);
  }
  function observe(cb: () => void) {
    productCategoriesMap.observeDeep(cb);
  }

  return {
    getAll,
    getOne,
    setOne,
    createOne,
    deleteOne,
    observe,
  };
}
