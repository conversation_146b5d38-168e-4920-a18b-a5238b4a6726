import * as Y from "yjs";
import type { Order } from "../model";

const ORDERS_NAMESPACE = "orders";

export function Orders(doc: Y.Doc) {
  const ordersMap = doc.getMap<Order>(ORDERS_NAMESPACE);

  function getAll() {
    return ordersMap.values();
  }
  function getOne(id: Order["id"]) {
    return ordersMap.get(id);
  }
  function setOne(id: Order["id"], order: Order) {
    return ordersMap.set(id, { ...order, id: id });
  }
  function createOne(newItem: Omit<Order, "id" | "createdAt">) {
    const id = crypto.randomUUID();
    const product: Order = {
      ...newItem,
      id,
      createdAt: new Date().toISOString(),
    };
    return setOne(id, product);
  }
  function deleteOne(id: Order["id"]) {
    return ordersMap.delete(id);
  }

  function observe(cb: () => void) {
    ordersMap.observeDeep(cb);
  }

  function observeOne(id: Order["id"], cb: () => void) {
    ordersMap.observe((event) => {
      if (event.keysChanged.has(id)) {
        cb();
      }
    });
  }

  return {
    getAll,
    getOne,
    setOne,
    createOne,
    deleteOne,
    observe,
    observeOne,
  };
}
