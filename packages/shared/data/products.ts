import * as Y from "yjs";
import { Product } from "../model";

const PRODUCTS_NAMESPACE = "products";

export function Products(doc: Y.Doc) {
  const productsMap = doc.getMap<Product>(PRODUCTS_NAMESPACE);

  function getAll() {
    return productsMap.values();
  }
  function getOne(id: Product["id"]) {
    return productsMap.get(id);
  }
  function setOne(id: Product["id"], product: Product) {
    return productsMap.set(id, { ...product, id: id });
  }
  function createOne(newItem: Omit<Product, "id">) {
    const id = crypto.randomUUID();
    const product: Product = {
      ...newItem,
      id,
    };
    return setOne(id, product);
  }
  function deleteOne(id: Product["id"]) {
    return productsMap.delete(id);
  }

  function observe(cb: () => void) {
    productsMap.observeDeep(cb);
  }

  return {
    getAll,
    getOne,
    setOne,
    createOne,
    deleteOne,
    observe,
  };
}
