{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"esModuleInterop": true, "skipLibCheck": true, "target": "es2022", "resolveJsonModule": true, "isolatedModules": true, "moduleDetection": "force", "strict": true, "noUncheckedIndexedAccess": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "module": "ESNext", "noEmit": true, "lib": ["es2022", "dom", "dom.iterable"], "paths": {"@kassierer/shared": ["./packages/data"], "@kassierer/model": ["./packages/model"]}}, "exclude": ["node_modules"]}