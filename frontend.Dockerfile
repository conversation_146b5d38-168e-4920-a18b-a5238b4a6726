FROM node:20-alpine AS build
ENV NODE_ENV=production
WORKDIR /usr/src/app

# Copy root package.json and lockfile
COPY package.json ./
COPY package-lock.json ./

# Copy the docs package.json
COPY apps/frontend/package.json ./apps/frontend/package.json
COPY ./turbo.json ./
COPY ./packages ./packages
RUN npm install

# Build
COPY ./apps/frontend ./apps/frontend
RUN npm run build-frontend

# Stage 2: Serve with nginx
FROM nginx:alpine
RUN rm /etc/nginx/conf.d/default.conf
COPY devops/frontend.conf /etc/nginx/conf.d
COPY --from=build /usr/src/app/apps/frontend/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
