import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { YModule } from './y-websocket/y.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    YModule,
    AuthModule,
    UsersModule,
    TypeOrmModule.forRoot({
      type: 'sqlite',
      database: 'data.db',
      // entities: [],
      autoLoadEntities: true,
      synchronize: true, // should be false in prod
    }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
