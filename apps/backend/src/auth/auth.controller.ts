import { PublicUser } from '@kassierer/shared/auth';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Request,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { JwtPayload } from 'src/auth/auth.model';
import { AuthService } from 'src/auth/auth.service';
import { User } from 'src/users/user.entity';
import { UsersService } from 'src/users/users.service';

type SignInDto = {
  email: string;
  password: string;
};

type SignUpDto = {
  email: string;
  password: string;
};

@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private userService: UsersService,
  ) {}

  // Always a user, not a client
  @HttpCode(HttpStatus.CREATED)
  @Post('signup')
  async signUp(@Body() dto: SignUpDto) {
    if (!dto.email || !dto.password) {
      throw new BadRequestException();
    }
    const user = await this.userService.createOne(dto.email, dto.password);
    const publicUser: Omit<
      User,
      'password' | 'isActive' | 'clients' | 'yDocumentName' | 'document'
    > = {
      email: user.email,
    };
    return publicUser;
  }

  // Always a user, not a client
  @HttpCode(HttpStatus.OK)
  @Post('login')
  signIn(@Body() dto: SignInDto) {
    if (!dto?.email || !dto?.password) {
      throw new BadRequestException();
    }
    return this.authService.signIn(dto.email, dto.password);
  }

  @UseGuards(AuthGuard)
  @Get('profile')
  profile(@Request() req): PublicUser {
    const { sub, isUserClient } = req.user as JwtPayload;

    if (isUserClient) {
      return {
        id: sub,
      };
    }

    return {
      id: sub,
      email: sub,
    };
  }

  @UseGuards(AuthGuard)
  @Post('client')
  async createClient(@Request() req) {
    const { sub, isUserClient } = req.user as JwtPayload;

    if (isUserClient) {
      throw new UnauthorizedException();
    }

    // create and return
    const { id } = await this.userService.createClientForUser(sub);
    // sign a jwt
    return this.authService.signClient(id);
  }
}
