import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from 'src/users/user.entity';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { UserClient } from 'src/users/userClient.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(UserClient)
    private clientRepo: Repository<UserClient>,
  ) {}

  async findUserForClient(clientId: string) {
    const { user } = await this.clientRepo.findOneByOrFail({
      id: clientId,
    });
    return this.findOne(user);
  }

  async findOne(email: string): Promise<User | undefined> {
    return this.userRepo.findOneBy({
      email: email,
    });
  }

  async createOne(email: string, password: string): Promise<User> {
    const salt = await bcrypt.genSalt();
    const hash = await bcrypt.hash(password, salt);

    return this.userRepo.save({
      email: email,
      yDocumentName: email,
      password: hash,
      isActive: true,
    });
  }

  async createClientForUser(user: string) {
    try {
      return this.clientRepo.save({
        isActive: true,
        user: user,
      });
    } catch (e) {
      console.error(e);
    }
  }
}
