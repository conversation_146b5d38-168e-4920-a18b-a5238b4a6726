import { UserClient } from 'src/users/userClient.entity';
import { UserDocument } from 'src/y-websocket/UserDocument.entity';
import { Entity, Column, PrimaryColumn, OneToMany, OneToOne } from 'typeorm';

@Entity()
export class User {
  @PrimaryColumn()
  email: string;

  @Column()
  password: string;

  @Column()
  yDocumentName: string;

  @Column({ default: true })
  isActive: boolean;

  @OneToMany(() => UserClient, (client) => client.user)
  clients: UserClient[];

  @OneToOne(() => UserDocument, (userDocument) => userDocument.user)
  document: UserDocument[];
}
