import { Module } from '@nestjs/common';
import { YjsGateway } from './y.gateway';
import { UsersModule } from 'src/users/users.module';
import { JwtModule } from '@nestjs/jwt';
import { authConstants } from 'src/auth/constants';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserDocument } from 'src/y-websocket/UserDocument.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserDocument]),
    UsersModule,
    JwtModule.register({
      global: true,
      secret: authConstants.jwtSecret,
      signOptions: { expiresIn: '30d' },
    }),
  ],
  providers: [YjsGateway],
})
export class YModule {}
