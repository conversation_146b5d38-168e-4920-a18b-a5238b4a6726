import { useYjsStore } from "@/stores/yjsStore";
import { Products } from "@kassierer/shared/data/products";
import type { Product } from "@kassierer/shared/model";
import { create } from "zustand";

type ProductStore = {
  products: Product[];
  productsMap: Record<Product["id"], Product>;
  deleteProduct: (id: Product["id"]) => void;
  createProduct: (product: Omit<Product, "id">) => void;
  setProduct: (id: Product["id"], product: Product) => void;
  initialize: () => void;
};

export const useProducts = create<ProductStore>((set, get) => ({
  products: [],
  get productsMap() {
    return get().products.reduce<Record<Product["id"], Product>>(
      (pMap, product) => {
        pMap[product.id] = product;
        return pMap;
      },
      {},
    );
  },
  deleteProduct: () => {},
  createProduct: () => {},
  setProduct: () => {},

  initialize: () => {
    const doc = useYjsStore.getState().doc;
    if (!doc) return;

    const { observe, getAll, deleteOne, createOne, setOne } = Products(doc);
    observe(() => set({ products: Array.from(getAll()) }));

    // Update the store methods
    set({
      products: Array.from(getAll()),
      deleteProduct: deleteOne,
      createProduct: createOne,
      setProduct: setOne,
    });
  },
}));

// Subscribe to YJS store changes and reinitialize when doc changes
useYjsStore.subscribe((state) => {
  if (state.doc) {
    useProducts.getState().initialize();
  }
});

// Initialize if doc is already available
if (useYjsStore.getState().doc) {
  useProducts.getState().initialize();
}
