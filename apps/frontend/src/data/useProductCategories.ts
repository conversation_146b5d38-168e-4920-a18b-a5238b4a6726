import { useYjsStore } from "@/stores/yjsStore";
import { ProductCategories } from "@kassierer/shared/data/product-category";
import type { ProductCategory } from "@kassierer/shared/model";
import { create } from "zustand";

type ProductCategoryStore = {
  categories: ProductCategory[];
  deleteCategory: (id: ProductCategory["id"]) => void;
  createCategory: (category: Omit<ProductCategory, "id">) => void;
  updateCategory: (id: ProductCategory["id"], category: ProductCategory) => void;
  initialize: () => void;
};

export const useProductCategories = create<ProductCategoryStore>((set) => ({
  categories: [],
  deleteCategory: () => {},
  createCategory: () => {},
  updateCategory: () => {},

  initialize: () => {
    const doc = useYjsStore.getState().doc;
    if (!doc) return;

    const { observe, getAll, deleteOne, createOne, setOne } = ProductCategories(doc);
    observe(() => set({ categories: Array.from(getAll()) }));

    // Update the store methods
    set({
      categories: Array.from(getAll()),
      deleteCategory: deleteOne,
      createCategory: createOne,
      updateCategory: setOne,
    });
  },
}));

// Subscribe to YJS store changes and reinitialize when doc changes
useYjsStore.subscribe((state) => {
  if (state.doc) {
    useProductCategories.getState().initialize();
  }
});

// Initialize if doc is already available
if (useYjsStore.getState().doc) {
  useProductCategories.getState().initialize();
}