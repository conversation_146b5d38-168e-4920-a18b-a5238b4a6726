import { useYjsStore } from "@/stores/yjsStore";
import { Orders } from "@kassierer/shared/data/orders";
import type { Order } from "@kassierer/shared/model";
import { create } from "zustand";

type OrderStore = {
  orders: Order[];
  deleteOrder: (id: Order["id"]) => void;
  createOrder: (order: Omit<Order, "id" | "createdAt">) => void;
  updateOrder: (id: Order["id"], order: Order) => void;
  initialize: () => void;
};

export const useOrders = create<OrderStore>((set) => ({
  orders: [],
  deleteOrder: () => {},
  createOrder: () => {},
  updateOrder: () => {},

  initialize: () => {
    const doc = useYjsStore.getState().doc;
    if (!doc) return;

    console.log('init orders');

    const { observe, getAll, deleteOne, createOne, setOne } = Orders(doc);

    // updates from yjs
    observe(() => {
      const newOrders = Array.from(getAll());
      set({ orders: newOrders });
    });

    // Update the store methods
    set({
      orders: Array.from(getAll()),
      deleteOrder: deleteOne,
      createOrder: createOne,
      updateOrder: setOne,
    });
  },
}));

// Subscribe to YJS store changes and reinitialize when doc changes
useYjsStore.subscribe((state) => {
  if (state.doc) {
    useOrders.getState().initialize();
  }
});

// Initialize if doc is already available
if (useYjsStore.getState().doc) {
  useOrders.getState().initialize();
}