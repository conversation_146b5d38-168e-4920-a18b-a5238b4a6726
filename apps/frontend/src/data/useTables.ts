import { useYjsStore } from "@/stores/yjsStore";
import { Tables } from "@kassierer/shared/data/table";
import type { Table } from "@kassierer/shared/model";
import { create } from "zustand";

type TableStore = {
  tables: Table[];
  createTable: (table: Omit<Table, "id">) => void;
  deleteTable: (id: Table["id"]) => void;
  setTable: (id: Table["id"], table: Table) => void;
  initialize: () => void;
};

export const useTables = create<TableStore>((set) => ({
  tables: [],
  createTable: () => {},
  deleteTable: () => {},
  setTable: () => {},

  initialize: () => {
    const doc = useYjsStore.getState().doc;
    if (!doc) return;

    const { observe, getAll, createOne, deleteOne, setOne } = Tables(doc);
    observe(() => set({ tables: Array.from(getAll()) }));

    // Update the store methods
    set({
      tables: Array.from(getAll()),
      createTable: createOne,
      deleteTable: deleteOne,
      setTable: setOne,
    });
  },
}));

// Subscribe to YJS store changes and reinitialize when doc changes
useYjsStore.subscribe((state) => {
  if (state.doc) {
    useTables.getState().initialize();
  }
});

// Initialize if doc is already available
if (useYjsStore.getState().doc) {
  useTables.getState().initialize();
}