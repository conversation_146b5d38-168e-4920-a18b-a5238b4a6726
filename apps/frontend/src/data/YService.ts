import * as Y from "yjs";
import { IndexeddbPersistence } from "y-indexeddb";
import { WebsocketProvider } from "y-websocket";

type SetupCycle = { isActive: boolean };
function newSetupCycle(): SetupCycle {
  return {
    isActive: true,
  };
}

class YServiceClass {
  private localPersistance: IndexeddbPersistence | undefined;
  private wsProvider: WebsocketProvider | undefined;
  private onSyncedCallbacks: (() => void)[] = [];
  private onConnectedCallbacks: (() => void)[] = [];
  private onDisonnectedCallbacks: (() => void)[] = [];
  private setupCycle: SetupCycle | undefined = undefined;

  constructor() {
    this.setupDocAndConnections = this.setupDocAndConnections.bind(this);
    this.onSynced = this.onSynced.bind(this);
    this.onConnected = this.onConnected.bind(this);
    this.disconnect = this.disconnect.bind(this);
    this.handleLocalSynced = this.handleLocalSynced.bind(this);
    this.handleWebsocketStatus = this.handleWebsocketStatus.bind(this);
  }

  setupDocAndConnections(doc: Y.Doc, roomName?: string, authToken?: string) {
    this.disconnect(this.setupCycle);

    const newCycle = newSetupCycle();
    this.setupCycle = newCycle;

    doc.on("destroy", () => this.disconnect(newCycle));

    if (newCycle.isActive) {
      this.localPersistance = new IndexeddbPersistence(
        roomName ?? "kassierer-doc",
        doc,
      );
      this.localPersistance.on("synced", this.handleLocalSynced);
    }

    if (authToken && newCycle.isActive) {
      console.error("setup ws connect");
      this.wsProvider = new WebsocketProvider(
        import.meta.env["VITE_WSSERVER"],
        "yjs",
        doc,
        {
          params: {
            auth_token: authToken,
          },
        },
      );
      this.wsProvider.on("status", this.handleWebsocketStatus);
    }
  }

  onSynced(cb: () => void) {
    this.onSyncedCallbacks.push(cb);
  }

  onConnected(cb: () => void) {
    this.onConnectedCallbacks.push(cb);
  }

  onDisconnected(cb: () => void) {
    this.onDisonnectedCallbacks.push(cb);
  }

  private disconnect(setupCycle?: SetupCycle) {
    if (setupCycle) {
      setupCycle.isActive = false;
    }

    if (this.localPersistance) {
      this.localPersistance.off("synced", this.handleLocalSynced);
      this.localPersistance.destroy();
      this.localPersistance = undefined;
    }
    if (this.wsProvider) {
      this.wsProvider.disconnect();
      this.wsProvider.off("status", this.handleWebsocketStatus);
      this.wsProvider.destroy();
      this.wsProvider = undefined;
    }
  }

  private handleLocalSynced() {
    this.onSyncedCallbacks.forEach((cb) => cb());
    console.log("indexeddb synced");
  }

  private handleWebsocketStatus(event: { status: string }) {
    console.log("ws status", event.status);

    if (event.status === "connected") {
      this.onConnectedCallbacks.forEach((cb) => cb());
      return;
    }
    if (event.status === "disconnected") {
      this.onDisonnectedCallbacks.forEach((cb) => cb());
      return;
    }
  }
}

export const YService = new YServiceClass();
