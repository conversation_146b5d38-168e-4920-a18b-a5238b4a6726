import React from 'react';
import { AgGridReact } from 'ag-grid-react';
import type { ColDef, ICellRendererParams } from 'ag-grid-community';
import type {
  Order,
  Product,
  Table as TableModel,
} from "@kassierer/shared/model";
import { Card } from 'primereact/card';
import { Button } from "primereact/button";

import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';

type Props = {
  orders: Order[];
  tables: TableModel[];
  productsMap: Record<string, Product>;
  onDelete: (table: Order) => void;
};

// Action cell renderer component
const ActionCellRenderer = ({ data, onDelete }: { data: Order; onDelete: (order: Order) => void }) => {
  return (
    <div className="flex gap-2">
      <Button
        label="Löschen"
        icon="pi pi-trash"
        severity="danger"
        size="small"
        onClick={() => onDelete(data)}
      />
    </div>
  );
};

// Table cell renderer component
const TableCellRenderer = ({ data, tables }: { data: Order; tables: TableModel[] }) => {
  const orderTable = tables.find((t) => t.id === data.tableId);
  return <span>{orderTable?.name || '-'}</span>;
};

// Content cell renderer component
const ContentCellRenderer = ({ data, productsMap }: { data: Order; productsMap: Record<string, Product> }) => {
  return (
    <div>
      {data.content.map((orderItem, index) => (
        <div key={index} className="mb-1">
          <span className="font-medium">{productsMap[orderItem.productId]?.name || 'Unknown Product'}</span>
          <span className="text-gray-600 ml-2">({orderItem.items.length})</span>
        </div>
      ))}
    </div>
  );
};

export const AdminOrderPageTable: React.FC<Props> = ({
  orders,
  tables,
  productsMap,
  onDelete,
}) => {
  const columnDefs: ColDef<Order>[] = [
    {
      headerName: 'Tisch',
      field: 'tableId',
      width: 150,
      cellRenderer: (params: ICellRendererParams<Order>) => (
        <TableCellRenderer data={params.data!} tables={tables} />
      ),
      sortable: true,
      filter: true
    },
    {
      headerName: 'Inhalt',
      field: 'content',
      flex: 1,
      cellRenderer: (params: ICellRendererParams<Order>) => (
        <ContentCellRenderer data={params.data!} productsMap={productsMap} />
      ),
      sortable: false,
      filter: false
    },
    {
      headerName: 'Aktionen',
      width: 150,
      cellRenderer: (params: ICellRendererParams<Order>) => (
        <ActionCellRenderer data={params.data!} onDelete={onDelete} />
      ),
      sortable: false,
      filter: false
    }
  ];

  return (
    <Card title="Bestellungen">
      <div className="ag-theme-alpine" style={{ height: 400, width: '100%' }}>
        <AgGridReact
          rowData={orders}
          columnDefs={columnDefs}
          defaultColDef={{
            resizable: true,
            sortable: true,
            filter: true
          }}
          pagination={true}
          paginationPageSize={10}
          domLayout="autoHeight"
        />
      </div>
    </Card>
  );
};
