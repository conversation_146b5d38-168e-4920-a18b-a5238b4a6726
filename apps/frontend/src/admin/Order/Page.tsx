import { useTables } from "@/data/useTables";
import { useOrders } from "@/data/useOrders";
import { AdminOrderPageTable } from "@/admin/Order/Table";
import { useProducts } from "@/data/useProducts";

function AdminOrderPage() {
  const tables = useTables(state => state.tables);
  const productsMap = useProducts(state => state.productsMap);

  // orders
  const orders = useOrders((state) => state.orders);
  const deleteOrder = useOrders((state) => state.deleteOrder);

  return (
    <div className="grid">
      <div className="col-12">
        <AdminOrderPageTable
          orders={orders}
          tables={tables}
          productsMap={productsMap}
          onDelete={(order) => {
            deleteOrder(order.id);
          }}
        />
      </div>
    </div>
  );
}

export default AdminOrderPage;
