import React from 'react';
import { AgGridReact } from 'ag-grid-react';
import type { ColDef, ICellRendererParams } from 'ag-grid-community';
import type { ProductCategory } from "@kassierer/shared/model";
import { Card } from 'primereact/card';
import { But<PERSON> } from "primereact/button";
import { NavLink } from "react-router-dom";

import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';

// Action cell renderer component
const ActionCellRenderer = ({ data, onDelete }: { data: ProductCategory; onDelete: (category: ProductCategory) => void }) => {
  return (
    <div className="flex gap-2">
      <Button
        label="Löschen"
        icon="pi pi-trash"
        severity="danger"
        size="small"
        onClick={() => onDelete(data)}
      />
    </div>
  );
};

// Name cell renderer component
const NameCellRenderer = ({ data }: { data: ProductCategory }) => {
  return (
    <NavLink
      to={`/admin/product-categories/${data.id}`}
      className="text-primary no-underline hover:underline"
    >
      {data.name}
    </NavLink>
  );
};

export const AdminProductCategoryTable: React.FC<{
  categories: ProductCategory[];
  onDelete: (category: ProductCategory) => void;
}> = ({ categories, onDelete }) => {
  const columnDefs: ColDef<ProductCategory>[] = [
    {
      headerName: 'Name',
      field: 'name',
      flex: 1,
      cellRenderer: NameCellRenderer,
      sortable: true,
      filter: true
    },
    {
      headerName: 'Aktionen',
      width: 150,
      cellRenderer: (params: ICellRendererParams<ProductCategory>) => (
        <ActionCellRenderer data={params.data!} onDelete={onDelete} />
      ),
      sortable: false,
      filter: false
    }
  ];

  return (
    <Card title="Produkt Kategorien">
      <div className="ag-theme-alpine" style={{ height: 400, width: '100%' }}>
        <AgGridReact
          rowData={categories}
          columnDefs={columnDefs}
          defaultColDef={{
            resizable: true,
            sortable: true,
            filter: true
          }}
          pagination={true}
          paginationPageSize={10}
          domLayout="autoHeight"
        />
      </div>
    </Card>
  );
};
