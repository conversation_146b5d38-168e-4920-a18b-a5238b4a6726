import React from 'react';
import { AgGridReact } from 'ag-grid-react';
import type { ColDef, ICellRendererParams } from 'ag-grid-community';
import { type Table as TableModel } from "@kassierer/shared/model";
import { Card } from 'primereact/card';
import { Button } from "primereact/button";

import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';

type Props = {
  tables: TableModel[];
  onDelete: (table: TableModel) => void;
};

// Action cell renderer component
const ActionCellRenderer = ({ data, onDelete }: { data: TableModel; onDelete: (table: TableModel) => void }) => {
  return (
    <div className="flex gap-2">
      <Button
        label="Löschen"
        icon="pi pi-trash"
        severity="danger"
        size="small"
        onClick={() => onDelete(data)}
      />
    </div>
  );
};

export const AdminTablesPageTable: React.FC<Props> = ({ tables, onDelete }) => {
  const columnDefs: ColDef<TableModel>[] = [
    {
      headerName: 'Name',
      field: 'name',
      flex: 1,
      sortable: true,
      filter: true
    },
    {
      headerName: 'Aktionen',
      width: 150,
      cellRenderer: (params: ICellRendererParams<TableModel>) => (
        <ActionCellRenderer data={params.data!} onDelete={onDelete} />
      ),
      sortable: false,
      filter: false
    }
  ];

  return (
    <Card title="Tische">
      <div className="ag-theme-alpine" style={{ height: 400, width: '100%' }}>
        <AgGridReact
          rowData={tables}
          columnDefs={columnDefs}
          defaultColDef={{
            resizable: true,
            sortable: true,
            filter: true
          }}
          pagination={true}
          paginationPageSize={10}
          domLayout="autoHeight"
        />
      </div>
    </Card>
  );
};
