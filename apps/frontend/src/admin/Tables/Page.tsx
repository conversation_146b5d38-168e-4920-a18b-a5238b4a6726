import { useState } from "react";
import { AdminTablesPageTable } from "@/admin/Tables/Table";
import { useTables } from "@/data/useTables";
import { Card } from 'primereact/card';
import { InputText } from "primereact/inputtext";
import { FloatLabel } from "primereact/floatlabel";
import { Button } from "primereact/button";

function AdminTablesPage() {
  const tables = useTables((state) => state.tables);
  const deleteTable = useTables((state) => state.deleteTable);
  const createTable = useTables((state) => state.createTable);

  const [tableName, setTableName] = useState('');

  const handleCreateTable = () => {
    if (tableName.trim()) {
      createTable({ name: tableName.trim() });
      setTableName('');
    }
  };

  return (
    <div className="grid">
      <div className="col-12">
        <Card title="Neuer Tisch" className="mb-4">
          <div className="formgrid grid">
            <div className="field col-12 md:col-8">
              <FloatLabel>
                <InputText
                  id="tableName"
                  value={tableName}
                  onChange={(e) => setTableName(e.target.value)}
                  placeholder="Name des neuen Tisches eingeben"
                />
                <label htmlFor="tableName">Tisch Name</label>
              </FloatLabel>
            </div>
            <div className="field col-12 md:col-4 flex align-items-end">
              <Button
                label="Erstellen"
                icon="pi pi-plus"
                onClick={handleCreateTable}
                disabled={!tableName.trim()}
                className="w-full"
              />
            </div>
          </div>
        </Card>
      </div>

      <div className="col-12">
        <AdminTablesPageTable
          tables={tables}
          onDelete={(table) => {
            deleteTable(table.id);
          }}
        />
      </div>
    </div>
  );
}

export default AdminTablesPage;
