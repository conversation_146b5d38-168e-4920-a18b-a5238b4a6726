import { create } from "zustand";
import * as Y from "yjs";
import { YService } from "@/data/YService";
import { useUserStore } from "@/stores/userStore";

interface YjsState {
  // State
  doc: Y.Doc | null;
  synced: boolean;
  connected: boolean;
  
  // Actions
  setSynced: (synced: boolean) => void;
  setConnected: (connected: boolean) => void;
  initializeDoc: () => void;
  setupWithToken: (token: string) => void;
  cleanup: () => void;
}

export const useYjsStore = create<YjsState>((set, get) => ({
  // Initial state
  doc: null,
  synced: false,
  connected: false,
  
  // Actions
  setSynced: (synced: boolean) => {
    set({ synced });
  },
  
  setConnected: (connected: boolean) => {
    set({ connected });
  },
  
  initializeDoc: () => {
    const newDoc = new Y.Doc();
    set({ doc: newDoc, synced: false, connected: false });
    
    // Setup initial connections without token
    YService.setupDocAndConnections(newDoc);
    
    // Setup callbacks
    YService.onSynced(() => {
      get().setSynced(true);
    });
    
    YService.onConnected(() => {
      get().setConnected(true);
    });
    
    YService.onDisconnected(() => {
      get().setConnected(false);
    });
  },
  
  setupWithToken: (token: string) => {
    const { doc } = get();
    if (!doc) {
      get().initializeDoc();
    }
    
    const newDoc = new Y.Doc();
    set({ doc: newDoc, synced: false, connected: false });
    
    // Setup connections with authentication token
    YService.setupDocAndConnections(newDoc, "", token);
    
    // Setup cleanup when doc is destroyed
    newDoc.on("destroy", () => {
      set({ synced: false, connected: false });
    });
  },
  
  cleanup: () => {
    const { doc } = get();
    if (doc) {
      doc.destroy();
    }
    set({ doc: null, synced: false, connected: false });
  },
}));

// Initialize the YJS store and set up token subscription
const initializeYjsStore = () => {
  const yjsStore = useYjsStore.getState();
  yjsStore.initializeDoc();
  
  // Subscribe to user token changes
  let previousToken: string | undefined;
  
  useUserStore.subscribe((state) => {
    const { token } = state;
    
    // Only setup new connection if token changed
    if (token !== previousToken) {
      previousToken = token;
      
      if (token) {
        yjsStore.setupWithToken(token);
      }
    }
  });
  
  // Check initial token
  const initialToken = useUserStore.getState().token;
  if (initialToken) {
    yjsStore.setupWithToken(initialToken);
  }
};

// Initialize on module load
initializeYjsStore();
