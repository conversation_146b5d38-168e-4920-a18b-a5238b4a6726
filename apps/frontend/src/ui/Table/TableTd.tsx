import { classNames } from "primereact/utils";
import { PropsWithChildren } from "react";

type Props = {
  align?: "left" | "right";
};

export const TableTd: React.FC<PropsWithChildren<Props>> = ({
  children,
  align,
}) => {
  return (
    <td
      className={classNames(
        "whitespace-nowrap px-3 py-4 text-sm text-gray-500",
        align === "right" ? "text-right" : "text-left",
      )}
    >
      {children}
    </td>
  );
};
