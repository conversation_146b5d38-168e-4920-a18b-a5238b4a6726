import { type FieldHookConfig, useField } from "formik";
import { type PropsWithChildren } from "react";

type Props = {
  config: string | FieldHookConfig<unknown>;
};

export const SelectInput: React.FC<PropsWithChildren<Props>> = ({
  config,
  children,
}) => {
  const [field, meta] = useField(config);
  return (
    <div>
      <label
        htmlFor="location"
        className="block text-sm font-medium leading-6 text-gray-900"
      >
        Location
      </label>
      <select
        {...field}
        className="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
      >
        {children}
      </select>
      {meta.error && meta.touched && <div>{meta.error}</div>}
    </div>
  );
};
