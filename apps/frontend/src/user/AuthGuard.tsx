import { useUserStore } from "@/stores/userStore";
import { type PropsWithChildren, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export const AuthGuard: React.FC<PropsWithChildren> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthPending, user } = useUserStore();

  useEffect(() => {
    if (!isAuthPending && !user) {
      navigate(`/login?navigate=${location.pathname}`);
    }
  }, [isAuthPending, user, navigate, location.pathname]);

  if (isAuthPending) {
    return <div>Loading</div>;
  }

  if (!user) {
    return <div>Unauthorized</div>;
  }

  return children;
};
