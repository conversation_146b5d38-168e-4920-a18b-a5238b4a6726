import { axiosInstance } from "@/utils/axios";
import type { PublicUser } from "@kassierer/shared/auth";

function login(email: string, password: string) {
  return axiosInstance.post<{ access_token: string }>("/auth/login", {
    email,
    password,
  });
}

function register(email: string, password: string) {
  return axiosInstance.post<{ access_token: string }>("/auth/signup", {
    email,
    password,
  });
}

async function getUser() {
  const { data } = await axiosInstance.get<PublicUser>("/auth/profile");
  return data;
}

export const UserService = {
  login,
  register,
  getUser,
};
