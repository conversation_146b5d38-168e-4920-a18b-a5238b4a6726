import { OrderListItem } from "./OderListItem";
import type { Order, Product } from "@kassierer/shared/model";
import React from "react";

type Props = {
  products: Record<Product["id"], Product>;
  orderContent: Order["content"];
  onIncrease: (product) => void;
  onDecrease: (product) => void;
  onDelete: (product) => void;
};

export const KassiererOrderList: React.FC<Props> = ({
  products,
  orderContent,
  onIncrease,
  onDecrease,
  onDelete,
}) => {
  return (
    <div className="flex flex-col gap-2 md:pt-0 p-2 overflow-y-auto overflow-x-hidden h-full">
      {orderContent.map((item) => {
        return (
          <OrderListItem
            key={`kassierer-item-${item.productId}`}
            product={products[item.productId]}
            quantity={item.items.length}
            onIncrease={onIncrease}
            onDecrease={onDecrease}
            onDelete={onDelete}
          />
        );
      })}
    </div>
  );
};
