import React from "react";
import { motion } from "framer-motion";
import type { Product } from "@kassierer/shared/model";
// import { useSetKassiererItem } from "@kassierer/kassierer-data";

type OrderListItemProps = {
  product: Product;
  quantity: number;
  onIncrease: (product: Product) => void;
  onDecrease: (product: Product) => void;
  onDelete: (product: Product) => void;
};

const numberFormat = new Intl.NumberFormat(undefined, {
  currency: "EUR",
  style: "currency",
});

export const OrderListItem: React.FC<OrderListItemProps> = ({
  product,
  quantity,
  onIncrease,
  onDecrease,
  onDelete,
}) => {
  const cost = (product.priceCents * quantity) / 100;

  return (
    <motion.div
      className="w-full flex justify-between items-center bg-gray-700 py-4 px-3 select-none"
      layout
      drag="x"
      dragSnapToOrigin={true}
      dragConstraints={{ right: 0 }}
      dragPropagation={false}
      onDragEnd={(event, info) => {
        if (info.velocity.x < -200) {
          onDelete(product);
        }
      }}
      onClick={() => onIncrease(product)}
    >
      <div className="flex items-center">
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onDecrease(product);
          }}
          className="pr-2"
        >
          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-red-500 text-white leading-none">
            -
          </div>
        </button>
        <motion.div
          key={`kassierer-item-${product.id}-quantity-${quantity}`}
          animate={{ scale: 1 }}
          initial={{ scale: 1.4 }}
          className="w-7 text-right mr-4"
        >
          {quantity}x
        </motion.div>
        <div className="pr-4">{product.name}</div>
      </div>
      <motion.div
        className="w-16 text-right"
        key={`kassierer-item-${product.id}-cost-${cost}`}
        animate={{ scale: 1 }}
        initial={{ scale: 1.2 }}
      >
        {numberFormat.format(cost)}
      </motion.div>
    </motion.div>
  );
};
