import { useProductCategories } from "@/data/useProductCategories";
import { useProducts } from "@/data/useProducts";
import type { Order, Product } from "@kassierer/shared/model";
import { useEffect, useState } from "react";
import layout from "@/kassierer/ui/kassierer-layout.module.css";
import { useUserSettings } from "@/data/useUserSettings";
import { OrderSidebar } from "@/kassierer/order/OrderSidebar";
import { KassiererOrderList } from "@/kassierer/order/OrderList/OrderList";
import { KassiererProductList } from "@/kassierer/order/ProductList/ProductList";
import { useOrders } from "@/data/useOrders";

function OrderView() {
  // Settings
  const darkMode = useUserSettings(state => state.darkMode);
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [darkMode]);

  // Products
  const products = useProducts((state) => state.products);
  const productsMap = useProducts((state) => state.productsMap);
  const categories = useProductCategories((state) => state.categories);

  // Tables
  /*
  const { observe: observerTables, getAll: getAllTables } = Tables(doc);
  const [tables, setTables] = useState<Table[]>(Array.from(getAllTables()));
  observerTables(() => setTables(Array.from(getAllTables())));
  */

  // const [selectedTableId, setSelectedTableId] = useState("");

  // Order
  const createOrder = useOrders((state) => state.createOrder);

  const [orderId] = useState(crypto.randomUUID());
  const [order, setOrder] = useState<Order>({
    id: orderId,
    content: [],
    createdAt: "",
    tableId: "",
    userId: 1,
  });

  const onCreateOrder = () => {
    createOrder(order);
  };

  const increaseItem = (product: Product) => {
    const existingItem = order.content.find(
      (item) => item.productId === product.id,
    );

    // if it's a completely new one
    if (!existingItem) {
      setOrder({
        ...order,
        content: [
          ...order.content,
          {
            productId: product.id,
            items: [{ payed: false, productId: product.id }],
          },
        ],
      });
      return;
    }

    setOrder({
      ...order,
      content: order.content.map((i) =>
        i.productId === product.id
          ? {
              ...i,
              items: [...i.items, { productId: product.id, payed: false }],
            }
          : i,
      ),
    });
  };

  const decreaseItem = (product: Product) => {
    const orderItems = order.content.find((oc) => oc.productId === product.id);

    // if it will be zero after the decrease
    if (!orderItems?.items.length) {
      removeItem(product);
      return;
    }

    setOrder({
      ...order,
      content: order.content.map((oc) =>
        oc.productId === product.id
          ? { ...oc, items: oc.items.slice(0, -1) }
          : oc,
      ),
    });
  };

  const removeItem = (product: Product) => {
    setOrder({
      ...order,
      content: order.content.filter((oc) => oc.productId !== product.id),
    });
  };

  return (
    <div
      className={
        "fixed top-0 left-0 w-full h-full dark:bg-black dark:text-white dark " +
        layout.wrapper
      }
    >
      <div className={layout.window}>
        <div className={layout.area_sidebar}>
          <OrderSidebar></OrderSidebar>
        </div>
        <div className={layout.area_upper}>
          <KassiererOrderList
            products={productsMap}
            orderContent={order.content}
            onIncrease={increaseItem}
            onDecrease={decreaseItem}
            onDelete={removeItem}
          />
        </div>
        <div className={layout.area_lower}>
          <button onClick={onCreateOrder}>ADD</button>
          <KassiererProductList
            products={products}
            categories={categories}
            onIncrease={increaseItem}
          />
        </div>
      </div>
    </div>
  );
}

export default OrderView;
