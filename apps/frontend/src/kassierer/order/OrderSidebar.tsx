import { SidebarBase } from "@/kassierer/ui/Sidebar/Sidebar.base";
import { classes } from "@/utils/classes";
import { PropsWithChildren } from "react";

export const OrderSidebar: React.FC = () => {
  return (
    <SidebarBase>
      <SidebarButton withBackground={true}>H</SidebarButton>
      <SidebarButton withBackground={false}>M</SidebarButton>
    </SidebarBase>
  );
};

const SidebarButton: React.FC<PropsWithChildren<{ withBackground }>> = ({
  children,
  withBackground,
}) => {
  return (
    <button
      className={classes(
        withBackground ? "dark:bg-gray-500" : "bg-transparent",
        "dark:text-white border-none flex items-center justify-center w-full aspect-square",
      )}
    >
      <div>{children}</div>
    </button>
  );
};
