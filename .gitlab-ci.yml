stages:
  - build
  - deploy

build_backend:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build -t $CI_REGISTRY/julianhandl/kassierer-crdt/kassierer-backend -f backend.Dockerfile .
    - docker push $CI_REGISTRY/julianhandl/kassierer-crdt/kassierer-backend:latest
  only:
  - master

build_frontend:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build -t $CI_REGISTRY/julianhandl/kassierer-crdt/kassierer-frontend -f frontend.Dockerfile .
    - docker push $CI_REGISTRY/julianhandl/kassierer-crdt/kassierer-frontend:latest
  only:
  - master

deploy:
  before_script:
  - 'command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )'
  - eval $(ssh-agent -s)
  - chmod 400 "$SSH_PRIVATE_KEY"
  - ssh-add "$SSH_PRIVATE_KEY"
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh
  only:
  - master
  script:
    - ssh -oStrictHostKeyChecking=no root@************ -p 12488 "hostname && echo 'Welcome!!!' > welcome.txt"
    - echo "Deploy your services to the server"
  when: manual
